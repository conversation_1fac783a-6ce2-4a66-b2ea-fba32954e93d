<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'Vertigo AMS - Admin Panel'); ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        /* Vertigo AMS Brand Colors */
        :root {
            --primary-500: #0068ff;
            --primary-600: #0056d6;
            --primary-700: #0045ad;
            --brand-800: #243b53;
            --brand-900: #003472;
            --success-500: #10b981;
            --warning-500: #f59e0b;
            --danger-500: #ef4444;
        }

        .gradient-primary {
            background: linear-gradient(135deg, #0068ff 0%, #0056d6 100%);
        }
        
        .gradient-brand {
            background: linear-gradient(135deg, #243b53 0%, #003472 100%);
        }

        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        .nav-shadow {
            box-shadow: 0 4px 20px rgba(0, 104, 255, 0.1);
        }

        .admin-sidebar {
            transition: width 0.3s ease;
        }

        .admin-sidebar.collapsed {
            width: 4rem;
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            .admin-sidebar {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 50;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 280px !important;
                max-width: 85vw;
            }

            .admin-sidebar.mobile-open {
                transform: translateX(0);
            }

            .admin-content {
                margin-left: 0 !important;
            }
        }

        /* Prevent horizontal scroll on small screens */
        @media (max-width: 640px) {
            .admin-sidebar {
                width: 100vw !important;
                max-width: 100vw !important;
            }
        }

        /* Safe area support for devices with notches */
        .safe-area-padding {
            padding-left: max(1rem, env(safe-area-inset-left));
            padding-right: max(1rem, env(safe-area-inset-right));
        }

        .safe-area-top {
            padding-top: max(1rem, env(safe-area-inset-top));
        }

        /* Improve mobile scrolling */
        .mobile-scroll {
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
        }

        /* Better mobile tap highlights */
        .mobile-touch-target {
            min-height: 44px;
            min-width: 44px;
            -webkit-tap-highlight-color: rgba(0, 104, 255, 0.1);
        }

        .menu-item {
            transition: all 0.2s ease;
        }

        .menu-item:hover {
            transform: translateX(4px);
        }

        /* Enhanced menu animations */
        .menu-item-active {
            background: linear-gradient(135deg, #0068ff 0%, #0056d6 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(0, 104, 255, 0.3);
        }

        /* Badge styles */
        .nav-badge {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        /* Status indicators */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .status-online { background-color: var(--success-500); }
        .status-warning { background-color: var(--warning-500); }
        .status-offline { background-color: var(--danger-500); }

        /* Submenu animations */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .submenu.open {
            max-height: 500px;
        }

        /* Mobile menu overlay */
        .mobile-overlay {
            display: none;
        }

        .mobile-overlay.show {
            display: block;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen bg-gray-100 overflow-hidden">
        <!-- Mobile Overlay -->
        <div id="mobile-overlay" class="mobile-overlay fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden" onclick="closeMobileMenu()"></div>

        <!-- Admin Sidebar -->
        <div id="admin-sidebar" class="admin-sidebar bg-white border-r border-gray-200 flex flex-col z-50 shadow-xl w-72 md:relative md:translate-x-0">
            <!-- Admin Header -->
            <div class="p-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 mr-4">
                            <svg viewBox="0 0 260 260" class="w-full h-full drop-shadow-sm">
                                <circle fill="#243b53" cx="130" cy="130" r="130"/>
                                <path fill="#fff" d="M184.38,167.7h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                                <path fill="#fff" d="M125.73,128.23v15.55h-13.22l2.79,3.7-2.79,4.66h13.22v15.56h-40.82v-15.54l.74-1.24,2.21-3.7-2.95-3.87v-10.93l2.51-4.2h38.31Z"/>
                                <path fill="#fff" d="M184.38,123.37h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
                                <path fill="#fff" d="M96.65,123.37v-20.91h-12.65v-18.55h44.35v18.55h-12.65v20.91h-19.04Z"/>
                                <polygon fill="#fff" points="227.59 173.63 224 172.2 223.61 169.86 221.71 171.29 218.13 169.86 224.52 164.98 226.31 165.69 227.59 173.63"/>
                                <polygon fill="#fff" points="31.03 173.63 34.62 172.2 35 169.86 36.9 171.29 40.48 169.86 34.1 164.98 32.3 165.69 31.03 173.63"/>
                            </svg>
                        </div>
                        <div class="hidden sm:block">
                            <h1 class="text-xl font-bold text-gray-900"><?php echo e(env('APP_NAME', 'Trust Actioneers')); ?></h1>
                            <p class="text-sm text-gray-500 flex items-center">
                                <span class="status-indicator status-online"></span>
                                Admin Panel
                            </p>
                        </div>
                    </div>
                    <button onclick="toggleSidebar()" class="hidden md:block p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Admin Navigation Menu -->
            <nav class="flex-1 p-4 overflow-y-auto mobile-scroll">
                <div class="space-y-2">
                    <!-- Dashboard -->
                    <div class="menu-item">
                        <a href="<?php echo e(route('admin.modernized')); ?>" class="flex items-center px-4 py-3 rounded-xl font-medium mobile-touch-target transition-all duration-200 <?php echo e(request()->is('admin-modernized') ? 'menu-item-active text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">
                            <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            </svg>
                            <span>Dashboard</span>
                        </a>
                    </div>





                    <!-- Sales -->
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Order::class)): ?>
                    <div class="menu-item">
                        <a href="<?php echo e(route('orders.modernized.index')); ?>" class="flex items-center px-4 py-3 rounded-xl font-medium mobile-touch-target transition-all duration-200 <?php echo e(request()->is('orders-modernized*') ? 'menu-item-active text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">
                            <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                            <span>Sales</span>
                        </a>
                    </div>
                    <?php endif; ?>

                    <!-- Auctions Section -->
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Auction::class)): ?>
                    <div class="menu-item">
                        <button onclick="toggleSubmenu('auctions')" class="w-full flex items-center justify-between px-4 py-3 rounded-xl font-medium transition-all duration-200 mobile-touch-target <?php echo e(request()->is('auctions*', 'auction-types*', 'auction-listing*', 'auctions-modernized*', 'auction-types-modernized*', 'auction-listing-modernized*') ? 'bg-primary-50 text-primary-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>Auctions</span>
                            </div>
                            <svg id="auctions-arrow" class="h-4 w-4 transition-transform duration-200 <?php echo e(request()->is('auctions*', 'auction-types*', 'auction-listing*', 'auctions-modernized*', 'auction-types-modernized*', 'auction-listing-modernized*') ? 'rotate-180' : ''); ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="auctions-submenu" class="submenu ml-8 mt-2 space-y-1 <?php echo e(request()->is('auctions*', 'auction-types*', 'auction-listing*', 'auctions-modernized*', 'auction-types-modernized*', 'auction-listing-modernized*') ? 'open' : ''); ?>">
                            <a href="<?php echo e(route('auctions.modernized.index')); ?>" class="block px-4 py-2 text-sm rounded-lg mobile-touch-target transition-colors duration-200 <?php echo e(request()->is('auctions-modernized*') ? 'text-primary-700 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">Auction List</a>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\AuctionType::class)): ?>
                            <a href="<?php echo e(route('auction-types.modernized.index')); ?>" class="block px-4 py-2 text-sm rounded-lg mobile-touch-target transition-colors duration-200 <?php echo e(request()->is('auction-types-modernized*') ? 'text-primary-700 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">Auction Categories</a>
                            <?php endif; ?>
                            <a href="<?php echo e(route('auction-listing.modernized.create')); ?>" class="block px-4 py-2 text-sm rounded-lg mobile-touch-target transition-colors duration-200 <?php echo e(request()->is('auction-listing-modernized/create') ? 'text-primary-700 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">Create Auction</a>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Items Section -->
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Item::class)): ?>
                    <div class="menu-item">
                        <button onclick="toggleSubmenu('items')" class="w-full flex items-center justify-between px-4 py-3 rounded-xl font-medium transition-all duration-200 mobile-touch-target <?php echo e(request()->is('items*', 'items-modernized*') ? 'bg-primary-50 text-primary-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                <span>Items</span>
                            </div>
                            <svg id="items-arrow" class="h-4 w-4 transition-transform duration-200 <?php echo e(request()->is('items*', 'items-modernized*') ? 'rotate-180' : ''); ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="items-submenu" class="submenu ml-8 mt-2 space-y-1 <?php echo e(request()->is('items*', 'items-modernized*') ? 'open' : ''); ?>">
                            <a href="<?php echo e(route('items.modernized.index')); ?>" class="block px-4 py-2 text-sm rounded-lg mobile-touch-target transition-colors duration-200 <?php echo e(request()->is('items-modernized') && !request()->is('items-modernized/create') ? 'text-primary-700 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">Auction Items</a>
                            <a href="<?php echo e(route('items.modernized.create')); ?>" class="block px-4 py-2 text-sm rounded-lg mobile-touch-target transition-colors duration-200 <?php echo e(request()->is('items-modernized/create') ? 'text-primary-700 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">Add Item</a>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Adverts -->
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Advert::class)): ?>
                    <div class="menu-item">
                        <a href="<?php echo e(route('adverts.index')); ?>" class="flex items-center px-4 py-3 rounded-xl font-medium mobile-touch-target transition-all duration-200 <?php echo e(request()->is('adverts*') ? 'menu-item-active text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">
                            <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                            </svg>
                            <span>Adverts</span>
                        </a>
                    </div>
                    <?php endif; ?>

                    <!-- Deposits -->
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Transaction::class)): ?>
                    <div class="menu-item">
                        <a href="<?php echo e(route('transactions.index')); ?>" class="flex items-center px-4 py-3 rounded-xl font-medium mobile-touch-target transition-all duration-200 <?php echo e(request()->is('transactions*') ? 'menu-item-active text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">
                            <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            <span>Deposits</span>
                        </a>
                    </div>
                    <?php endif; ?>

                    <!-- Section Divider -->
                    <div class="px-4 py-2">
                        <div class="border-t border-gray-200"></div>
                    </div>

                    <!-- Reports Section -->
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\User::class)): ?>
                    <div class="menu-item">
                        <button onclick="toggleSubmenu('reports')" class="w-full flex items-center justify-between px-4 py-3 rounded-xl font-medium transition-all duration-200 mobile-touch-target <?php echo e(request()->is('*-report*') ? 'bg-primary-50 text-primary-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <span>Reports</span>
                            </div>
                            <svg id="reports-arrow" class="h-4 w-4 transition-transform duration-200 <?php echo e(request()->is('*-report*') ? 'rotate-180' : ''); ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="reports-submenu" class="submenu ml-8 mt-2 space-y-1 <?php echo e(request()->is('*-report*') ? 'open' : ''); ?>">
                            <a href="/winners-report" class="block px-4 py-2 text-sm rounded-lg mobile-touch-target transition-colors duration-200 <?php echo e(request()->is('winners-report*') ? 'text-primary-700 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">Winners Report</a>
                            <a href="/sales-report" class="block px-4 py-2 text-sm rounded-lg mobile-touch-target transition-colors duration-200 <?php echo e(request()->is('sales-report*') ? 'text-primary-700 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">Sales Report</a>
                            <a href="/inventory-report" class="block px-4 py-2 text-sm rounded-lg mobile-touch-target transition-colors duration-200 <?php echo e(request()->is('inventory-report*') ? 'text-primary-700 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">Inventory Report</a>
                            <a href="/refund-list-report" class="block px-4 py-2 text-sm rounded-lg mobile-touch-target transition-colors duration-200 <?php echo e(request()->is('refund-list-report*') ? 'text-primary-700 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">Refund List</a>
                            <a href="/deposits-report" class="block px-4 py-2 text-sm rounded-lg mobile-touch-target transition-colors duration-200 <?php echo e(request()->is('deposits-report*') ? 'text-primary-700 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">Deposits Report</a>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- User Management Section -->
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\User::class)): ?>
                    <div class="menu-item">
                        <button onclick="toggleSubmenu('users')" class="w-full flex items-center justify-between px-4 py-3 rounded-xl font-medium transition-all duration-200 mobile-touch-target <?php echo e(request()->is('users*', 'roles*', 'statuses*') ? 'bg-primary-50 text-primary-700' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                                <span>User Management</span>
                            </div>
                            <svg id="users-arrow" class="h-4 w-4 transition-transform duration-200 <?php echo e(request()->is('users*', 'roles*', 'statuses*') ? 'rotate-180' : ''); ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="users-submenu" class="submenu ml-8 mt-2 space-y-1 <?php echo e(request()->is('users*', 'roles*', 'statuses*') ? 'open' : ''); ?>">
                            <a href="<?php echo e(route('users.index')); ?>" class="block px-4 py-2 text-sm rounded-lg mobile-touch-target transition-colors duration-200 <?php echo e(request()->is('users*') ? 'text-primary-700 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">Users</a>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Role::class)): ?>
                            <a href="<?php echo e(route('roles.index')); ?>" class="block px-4 py-2 text-sm rounded-lg mobile-touch-target transition-colors duration-200 <?php echo e(request()->is('roles*') ? 'text-primary-700 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">Roles</a>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Status::class)): ?>
                            <a href="/statuses" class="block px-4 py-2 text-sm rounded-lg mobile-touch-target transition-colors duration-200 <?php echo e(request()->is('statuses*') ? 'text-primary-700 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">Status</a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Settings -->
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view-any', App\Models\Role::class)): ?>
                    <div class="menu-item">
                        <a href="<?php echo e(route('settings.index')); ?>" class="flex items-center px-4 py-3 rounded-xl font-medium mobile-touch-target transition-all duration-200 <?php echo e(request()->is('settings*') ? 'menu-item-active text-white' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'); ?>">
                            <svg class="h-5 w-5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span>Settings</span>
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </nav>

            <!-- Admin User Info -->
            <div class="p-4 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-white">
                <div class="flex items-center">
                    <div class="h-12 w-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mr-3 shadow-lg">
                        <span class="text-white font-bold text-lg"><?php echo e(strtoupper(substr(auth()->user()->name ?? 'AD', 0, 2))); ?></span>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-semibold text-gray-900"><?php echo e(auth()->user()->name ?? 'Admin User'); ?></p>
                        <p class="text-xs text-gray-500 flex items-center">
                            <span class="status-indicator status-online"></span>
                            <?php echo e(auth()->user()->roles->first()->name ?? 'Administrator'); ?>

                        </p>
                    </div>
                    <div class="flex space-x-1">
                        <a href="<?php echo e(route('notifications')); ?>" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors duration-200" title="Notifications">
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                            </svg>
                        </a>
                        <form method="POST" action="<?php echo e(route('logout')); ?>" class="inline">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors duration-200" title="Logout">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden admin-content">
            <!-- Admin Top Bar -->
            <div class="bg-white border-b border-gray-200 px-4 sm:px-6 py-4 shadow-sm">
                <div class="flex items-center justify-between">
                    <!-- Mobile menu button -->
                    <button onclick="toggleMobileMenu()" class="md:hidden p-2 rounded-lg text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>

                    <div class="flex-1 md:flex-none">
                        <h2 class="text-xl sm:text-2xl font-bold text-gray-900"><?php echo $__env->yieldContent('page-title', 'Dashboard'); ?></h2>
                        <p class="text-sm text-gray-500 hidden sm:block"><?php echo $__env->yieldContent('page-subtitle', 'Welcome to your admin panel'); ?></p>
                    </div>

                    <div class="flex items-center space-x-2 sm:space-x-4">
                        <!-- Quick Actions -->
                        <div class="hidden sm:flex">
                            <?php echo $__env->yieldContent('quick-actions'); ?>
                        </div>

                        <!-- Notifications -->
                        <a href="<?php echo e(route('notifications')); ?>" class="relative p-2 text-gray-400 hover:text-gray-500 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 8A6 6 0 006 8c0 7-3 9-3 9h18s-3-2-3-9z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.73 21a2 2 0 01-3.46 0"></path>
                            </svg>
                            <?php if(isset($notificationCount) && $notificationCount > 0): ?>
                            <span class="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg"><?php echo e($notificationCount); ?></span>
                            <?php endif; ?>
                        </a>

                        <!-- Search -->
                        <div class="relative hidden lg:block">
                            <form method="GET" action="<?php echo e(request()->url()); ?>">
                                <input type="text" name="search" value="<?php echo e(request('search')); ?>" placeholder="Search..." class="w-48 xl:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200">
                                <svg class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </form>
                        </div>

                        <!-- Mobile Search Button -->
                        <button onclick="toggleMobileSearch()" class="lg:hidden p-2 text-gray-400 hover:text-gray-500 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Mobile Search Bar -->
                <div id="mobile-search" class="lg:hidden mt-4 hidden">
                    <form method="GET" action="<?php echo e(request()->url()); ?>">
                        <input type="text" name="search" value="<?php echo e(request('search')); ?>" placeholder="Search..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <svg class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </form>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="flex-1 p-4 sm:p-6 bg-gray-50 overflow-y-auto">
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </div>
    </div>

    <script>
        // Mobile menu functionality
        function toggleMobileMenu() {
            const sidebar = document.getElementById('admin-sidebar');
            const overlay = document.getElementById('mobile-overlay');

            sidebar.classList.toggle('mobile-open');
            overlay.classList.toggle('show');
        }

        function closeMobileMenu() {
            const sidebar = document.getElementById('admin-sidebar');
            const overlay = document.getElementById('mobile-overlay');

            sidebar.classList.remove('mobile-open');
            overlay.classList.remove('show');
        }

        // Sidebar collapse functionality
        function toggleSidebar() {
            const sidebar = document.getElementById('admin-sidebar');
            sidebar.classList.toggle('collapsed');

            // Close all submenus when collapsing
            if (sidebar.classList.contains('collapsed')) {
                const submenus = document.querySelectorAll('.submenu');
                const arrows = document.querySelectorAll('[id$="-arrow"]');

                submenus.forEach(submenu => {
                    submenu.classList.remove('open');
                });

                arrows.forEach(arrow => {
                    arrow.classList.remove('rotate-180');
                });
            }
        }

        // Submenu toggle functionality
        function toggleSubmenu(menuName) {
            const sidebar = document.getElementById('admin-sidebar');

            // Don't toggle if sidebar is collapsed
            if (sidebar.classList.contains('collapsed')) {
                return;
            }

            const submenu = document.getElementById(menuName + '-submenu');
            const arrow = document.getElementById(menuName + '-arrow');

            if (submenu && arrow) {
                submenu.classList.toggle('open');
                arrow.classList.toggle('rotate-180');
            }
        }

        // Mobile search toggle
        function toggleMobileSearch() {
            const mobileSearch = document.getElementById('mobile-search');
            mobileSearch.classList.toggle('hidden');

            if (!mobileSearch.classList.contains('hidden')) {
                const searchInput = mobileSearch.querySelector('input');
                searchInput.focus();
            }
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('admin-sidebar');
            const mobileMenuButton = event.target.closest('button[onclick="toggleMobileMenu()"]');

            if (!sidebar.contains(event.target) && !mobileMenuButton && window.innerWidth < 768) {
                closeMobileMenu();
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 768) {
                closeMobileMenu();
            }
        });

        // Initialize tooltips for collapsed sidebar
        function initializeTooltips() {
            const sidebar = document.getElementById('admin-sidebar');
            const menuItems = sidebar.querySelectorAll('.menu-item a, .menu-item button');

            menuItems.forEach(item => {
                item.addEventListener('mouseenter', function(e) {
                    if (sidebar.classList.contains('collapsed')) {
                        const text = this.querySelector('span')?.textContent;
                        if (text) {
                            showTooltip(e.target, text);
                        }
                    }
                });

                item.addEventListener('mouseleave', function() {
                    hideTooltip();
                });
            });
        }

        function showTooltip(element, text) {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip show';
            tooltip.textContent = text;
            tooltip.id = 'sidebar-tooltip';

            document.body.appendChild(tooltip);

            const rect = element.getBoundingClientRect();
            tooltip.style.left = (rect.right + 10) + 'px';
            tooltip.style.top = (rect.top + rect.height / 2 - tooltip.offsetHeight / 2) + 'px';
        }

        function hideTooltip() {
            const tooltip = document.getElementById('sidebar-tooltip');
            if (tooltip) {
                tooltip.remove();
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeTooltips();
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/layouts/modernized-admin.blade.php ENDPATH**/ ?>