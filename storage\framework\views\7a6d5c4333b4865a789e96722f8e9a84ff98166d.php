<?php $__env->startSection('title', 'Add Item - Vertigo AMS'); ?>

<?php $__env->startSection('page-title', 'Add Item'); ?>
<?php $__env->startSection('page-subtitle', 'Create a new auction item'); ?>

<?php $__env->startSection('quick-actions'); ?>
<div class="flex space-x-2">
    <a href="<?php echo e(route('items.modernized.index')); ?>" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <span class="hidden lg:inline">Back to Items</span>
        <span class="lg:hidden">Back</span>
    </a>
    <a href="/inventory-report" class="flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        <span class="hidden lg:inline">View Reports</span>
        <span class="lg:hidden">Reports</span>
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Progress Steps -->
<div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 mb-8">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="flex items-center">
                <div class="h-8 w-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                <span class="ml-2 text-sm font-medium text-primary-600">Item Details</span>
            </div>
            <div class="h-px w-12 bg-gray-300"></div>
            <div class="flex items-center">
                <div class="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 font-bold text-sm">2</div>
                <span class="ml-2 text-sm font-medium text-gray-500">Images</span>
            </div>
            <div class="h-px w-12 bg-gray-300"></div>
            <div class="flex items-center">
                <div class="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 font-bold text-sm">3</div>
                <span class="ml-2 text-sm font-medium text-gray-500">Review</span>
            </div>
        </div>
        <div class="text-sm text-gray-500">
            Step 1 of 3
        </div>
    </div>
</div>

<!-- Item Form -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <div class="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                    <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                Create New Item
            </h3>
            <div class="flex items-center space-x-2">
                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">Draft</span>
                <span class="text-xs text-gray-500"><?php echo e(now()->format('M d, Y h:i A')); ?></span>
            </div>
        </div>
    </div>

    <div class="p-6">
        <!-- Form Instructions -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h4 class="text-sm font-medium text-blue-800">Creating a New Item</h4>
                    <div class="mt-1 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Fill in all required item details</li>
                            <li>Select the appropriate auction category</li>
                            <li>Set the target price for the item</li>
                            <li>Upload high-quality images</li>
                            <li>Review all information before submitting</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form -->
        <form method="POST" action="<?php echo e(route('items.store')); ?>" enctype="multipart/form-data" class="space-y-6">
            <?php echo csrf_field(); ?>
            <?php echo $__env->make('app.items.modernized-form-inputs', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="flex items-center space-x-4">
                    <a href="<?php echo e(route('items.modernized.index')); ?>" class="text-gray-600 hover:text-gray-900 transition-colors duration-200">
                        Cancel
                    </a>
                </div>
                <div class="flex items-center space-x-3">
                    <button type="button" class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg font-medium hover:bg-gray-200 transition-all duration-200">
                        Save as Draft
                    </button>
                    <button type="submit" class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-2 rounded-lg font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <svg class="h-4 w-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create Item
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Quick Tips -->
<div class="mt-8 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-200">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
        </div>
        <div class="ml-3">
            <h4 class="text-sm font-medium text-green-800 mb-2">💡 Quick Tips for Better Items</h4>
            <div class="text-sm text-green-700 space-y-1">
                <p><strong>High-Quality Images:</strong> Upload clear, well-lit photos from multiple angles</p>
                <p><strong>Detailed Descriptions:</strong> Include condition, dimensions, and unique features</p>
                <p><strong>Competitive Pricing:</strong> Research similar items to set appropriate target prices</p>
                <p><strong>Accurate Categories:</strong> Choose the most specific auction type for better visibility</p>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation and enhancement scripts can go here
    console.log('Item create form loaded');
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.modernized-admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/items/modernized-create.blade.php ENDPATH**/ ?>