<?php $__env->startSection('title', 'Item Details - Vertigo AMS'); ?>

<?php $__env->startSection('page-title', 'Item Details'); ?>
<?php $__env->startSection('page-subtitle', 'View item information and auction details'); ?>

<?php $__env->startSection('quick-actions'); ?>
<div class="flex space-x-2">
    <a href="<?php echo e(route('items.modernized.index')); ?>" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <span class="hidden lg:inline">Back to Items</span>
        <span class="lg:hidden">Back</span>
    </a>
    
    <?php if(!$item->closed_by): ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $item)): ?>
        <a href="<?php echo e(route('items.modernized.edit', $item)); ?>" class="flex items-center bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl">
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            <span class="hidden lg:inline">Edit Item</span>
            <span class="lg:hidden">Edit</span>
        </a>
        <?php endif; ?>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Item Status Banner -->
<div class="mb-8">
    <?php if($item->closed_by): ?>
    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="h-12 w-12 bg-white/20 rounded-lg flex items-center justify-center mr-4">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-xl font-bold">Item Sold</h3>
                    <p class="text-green-100">This item has been successfully sold</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-sm text-green-100">Closed by</p>
                <p class="font-semibold"><?php echo e(optional($item->closedBy)->name ?? 'System'); ?></p>
            </div>
        </div>
    </div>
    <?php else: ?>
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="h-12 w-12 bg-white/20 rounded-lg flex items-center justify-center mr-4">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-xl font-bold">Active Item</h3>
                    <p class="text-blue-100">This item is available for auction</p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-sm text-blue-100">Status</p>
                <p class="font-semibold"><?php echo e(optional($item->status)->name ?? 'Active'); ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Left Column - Item Images and Basic Info -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Item Images -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Item Images</h3>
            </div>
            <div class="p-6">
                <?php if($item->getMedia('media')->count() > 0): ?>
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <?php $__currentLoopData = $item->getMedia('media'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="group relative">
                        <a href="<?php echo e($image->getUrl()); ?>" data-fslightbox="gallery">
                            <img src="<?php echo e($image->getUrl()); ?>" alt="<?php echo e($item->name); ?>" 
                                 class="w-full h-64 object-cover rounded-lg border border-gray-200 group-hover:shadow-lg transition-shadow duration-200">
                            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                                <svg class="h-8 w-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </a>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <?php else: ?>
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No images</h3>
                    <p class="mt-1 text-sm text-gray-500">No images have been uploaded for this item.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Item Description -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Description</h3>
            </div>
            <div class="p-6">
                <?php if($item->description): ?>
                <div class="prose max-w-none text-gray-700">
                    <?php echo nl2br(e($item->description)); ?>

                </div>
                <?php else: ?>
                <p class="text-gray-500 italic">No description provided.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Right Column - Item Details -->
    <div class="space-y-6">
        <!-- Basic Information -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <div class="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <?php echo e($item->name); ?>

                </h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-500">Reference Number</span>
                    <span class="text-sm text-gray-900"><?php echo e($item->reference_number ?? '-'); ?></span>
                </div>
                
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-500">Item Code</span>
                    <span class="text-sm text-gray-900">
                        <?php if($item->code): ?>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium"><?php echo e($item->code); ?></span>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </span>
                </div>

                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-500">Supplier</span>
                    <span class="text-sm text-gray-900"><?php echo e(optional($item->user)->name ?? '-'); ?></span>
                </div>

                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-500">Category</span>
                    <div class="text-right">
                        <div class="text-sm text-gray-900"><?php echo e(optional($item->auctionType)->name ?? '-'); ?></div>
                        <div class="text-xs text-gray-500"><?php echo e(optional($item->auctionType)->type ?? '-'); ?></div>
                    </div>
                </div>

                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-500">Target Price</span>
                    <span class="text-lg font-bold text-green-600"><?php echo e(_money($item->target_amount)); ?></span>
                </div>

                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-500">Status</span>
                    <span class="text-sm text-gray-900"><?php echo e(optional($item->status)->name ?? '-'); ?></span>
                </div>

                <div class="flex justify-between items-center py-2">
                    <span class="text-sm font-medium text-gray-500">Date Added</span>
                    <span class="text-sm text-gray-900"><?php echo e($item->created_at ? $item->created_at->format('M d, Y h:i A') : '-'); ?></span>
                </div>
            </div>
        </div>

        <!-- Auction Schedule -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <div class="h-8 w-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                        <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2-2v14a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    Auction Schedule
                </h3>
            </div>
            <div class="p-6 space-y-4">
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-500">Start Date</span>
                    <div class="text-right">
                        <div class="text-sm text-gray-900"><?php echo e($item->date_from ? $item->date_from->format('M d, Y') : '-'); ?></div>
                        <div class="text-xs text-gray-500"><?php echo e($item->date_from ? $item->date_from->format('h:i A') : ''); ?></div>
                    </div>
                </div>

                <div class="flex justify-between items-center py-2">
                    <span class="text-sm font-medium text-gray-500">End Date</span>
                    <div class="text-right">
                        <div class="text-sm text-gray-900"><?php echo e($item->date_to ? $item->date_to->format('M d, Y') : '-'); ?></div>
                        <div class="text-xs text-gray-500"><?php echo e($item->date_to ? $item->date_to->format('h:i A') : ''); ?></div>
                    </div>
                </div>

                <?php if($item->date_from && $item->date_to): ?>
                <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                    <div class="flex items-center">
                        <svg class="h-4 w-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-sm text-blue-800">
                            Duration: <?php echo e($item->date_from->diffInDays($item->date_to)); ?> days
                        </span>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
                <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
            </div>
            <div class="p-6 space-y-3">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Item::class)): ?>
                <a href="<?php echo e(route('items.modernized.create')); ?>" class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add New Item
                </a>
                <?php endif; ?>

                <a href="<?php echo e(route('items.modernized.index')); ?>" class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                    </svg>
                    View All Items
                </a>

                <a href="/inventory-report" class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Inventory Report
                </a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.modernized-admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/items/modernized-show.blade.php ENDPATH**/ ?>