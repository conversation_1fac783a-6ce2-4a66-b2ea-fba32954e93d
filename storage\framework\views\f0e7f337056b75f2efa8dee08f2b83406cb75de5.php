

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Adverts</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Advert::class)): ?>
                <a href="<?php echo e(route('adverts.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add Advert
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="row">
        <?php $__empty_1 = true; $__currentLoopData = $adverts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $advert): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card h-100">
                <div class="card-img-top position-relative">
                    <img src="<?php echo e($advert->image ?? '/assets/img/160x160/img1.jpg'); ?>" class="card-img-top" alt="<?php echo e($advert->name); ?>" style="height: 200px; object-fit: cover;">

                    <?php if($advert->date_to): ?>
                    <div class="position-absolute bottom-0 end-0 p-2">
                        <span class="badge bg-primary">
                            <i class="bi bi-clock me-1"></i>
                            <?php echo e(\Carbon\Carbon::parse($advert->date_to)->diffForHumans()); ?>

                        </span>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="card-body">
                    <h5 class="card-title"><?php echo e($advert->name ?? '-'); ?></h5>
                    <p class="card-text text-muted small">
                        <?php echo e(\Illuminate\Support\Str::limit($advert->description ?? '-', 100)); ?>

                    </p>

                    <?php if($advert->date_from && $advert->date_to): ?>
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="bi bi-calendar-range me-1"></i>
                            <?php echo e(\Carbon\Carbon::parse($advert->date_from)->format('M d, Y')); ?> -
                            <?php echo e(\Carbon\Carbon::parse($advert->date_to)->format('M d, Y')); ?>

                        </small>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="card-footer bg-transparent border-top-0">
                    <div class="d-flex justify-content-between">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $advert)): ?>
                        <a href="<?php echo e(route('adverts.show', $advert)); ?>" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye me-1"></i>
                            View
                        </a>
                        <?php endif; ?>

                        <div>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $advert)): ?>
                            <a href="<?php echo e(route('adverts.edit', $advert)); ?>" class="btn btn-sm btn-primary me-1">
                                <i class="bi bi-pencil-square me-1"></i>
                                Edit
                            </a>
                            <?php endif; ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $advert)): ?>
                            <form action="<?php echo e(route('adverts.destroy', $advert)); ?>" method="POST" class="d-inline"
                                onsubmit="return confirm('<?php echo e(__('crud.common.are_you_sure')); ?>')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-sm btn-danger">
                                    <i class="bi bi-trash me-1"></i>
                                    Delete
                                </button>
                            </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <img src="/assets/svg/illustrations/oc-error.svg" alt="No Adverts Found" class="mb-3" style="max-width: 200px;">
                    <h3>No Adverts Found</h3>
                    <p class="text-muted">Start by creating your first advert.</p>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Advert::class)): ?>
                    <a href="<?php echo e(route('adverts.create')); ?>" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>
                        Add Advert
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/adverts/index.blade.php ENDPATH**/ ?>