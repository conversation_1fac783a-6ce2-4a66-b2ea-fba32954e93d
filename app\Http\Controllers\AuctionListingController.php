<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\User;
use App\Models\Status;
use App\Models\AuctionType;
use Illuminate\Http\Request;
use App\Http\Requests\AuctionTypeStoreRequest;
use App\Http\Requests\AuctionTypeUpdateRequest;
use Facades\App\Cache\Repo;

class AuctionListingController extends Controller
{

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', AuctionType::class);

        $auctionTypes = Repo::liveAuction();

        return view(
            'app.auction_listing.index',
            compact('auctionTypes')
        );
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize('create', AuctionType::class);

        $statuses = Status::pluck('name', 'id');
        $items = Item::whereNull('closed_by')->get();
        $users = User::pluck('name', 'id');

        return view(
            'app.auction_listing.create',
            compact('statuses', 'users', 'items')
        );
    }

    /**
     * @param \App\Http\Requests\AuctionTypeStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(AuctionTypeStoreRequest $request)
    {
        $this->authorize('create', AuctionType::class);

        $validated = $request->validated();

        $validated['type'] = 'live';

        $auctionType = AuctionType::create($validated);

        if( request()->media ) {
            foreach (request()->media as $file) {
                $auctionType->addMedia($file)->toMediaCollection("media");
            }
        }

        Item::whereIn("id", (request()->items ?? []) )->update([
            "auction_type_id" => $auctionType->id,
        ]);

        return redirect()
            ->route('auction-listing.show', $auctionType)
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\AuctionType $auctionType
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        $auctionType = AuctionType::find($id);      
        $this->authorize('view', $auctionType);
        $auctionTypes = AuctionType::get();
        $statuses = Status::pluck("name", 'id');

        return view('app.auction_listing.show', compact('auctionType', 'auctionTypes', 'statuses'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\AuctionType $auctionType
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, $id)
    {
        $auctionType = AuctionType::find($id);
        $this->authorize('update', $auctionType);

        $statuses = Status::pluck('name', 'id');
        $users = User::pluck('name', 'id');
        $items = Item::whereNull('closed_by')->get();

        return view(
            'app.auction_listing.edit',
            compact('auctionType', 'statuses', 'users', 'items')
        );
    }

    /**
     * @param \App\Http\Requests\AuctionTypeUpdateRequest $request
     * @param \App\Models\AuctionType $auctionType
     * @return \Illuminate\Http\Response
     */
    public function update( AuctionTypeUpdateRequest $request, $id) {
        $auctionType = AuctionType::find($id);
        $this->authorize('update', $auctionType);

        $validated = $request->validated();

        $validated['type'] = 'live';

        $auctionType->update($validated);
        Item::where("auction_type_id", $id)->update(["auction_type_id" => null]);
        Item::whereIn("id", (request()->items ?? []))->update([
            "auction_type_id" => $auctionType->id,
        ]);

        if( request()->media ) {
            foreach (request()->media as $file) {
                $auctionType->addMedia($file)->toMediaCollection("media");
            }
        }

        return redirect()
            ->route('auction-listing.index')
            ->withSuccess(__('crud.common.saved'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\AuctionType $auctionType
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $auctionType = AuctionType::find($id);
        $this->authorize('delete', $auctionType);

        $auctionType->items()->update(['auction_type_id' => null]);

        $auctionType->delete();

        return redirect()
            ->route('auction-listing.index')
            ->withSuccess(__('crud.common.removed'));
    }

    /**
     * Modernized create view for auction listing
     */
    public function modernizedCreate(Request $request)
    {
        $this->authorize('create', AuctionType::class);

        $statuses = Status::pluck('name', 'id');
        $items = Item::whereNull('closed_by')->get();
        $users = User::pluck('name', 'id');

        return view(
            'admin.modernized-auction-listing-create',
            compact('statuses', 'users', 'items')
        );
    }
}
