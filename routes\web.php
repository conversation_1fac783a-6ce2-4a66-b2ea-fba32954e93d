<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ItemController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\AdvertController;
use App\Http\Controllers\StatusController;
use App\Http\Controllers\AccountController;
use App\Http\Controllers\AuctionController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\AuctionTypeController;
use App\Http\Controllers\AuctionListingController;
use App\Http\Controllers\TransactionController;

use App\Http\Controllers\DataVerseController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ZoomController;

use Spatie\Permission\Models\Permission;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\SupplierController;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use App\Http\Controllers\Auth\VerificationController;
use Illuminate\Http\Request;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Test route for auction components
Route::get('/test', function () {
    $data = \App\Models\Item::paginate();
    return view('test', compact('data'));
});


Auth::routes();

// Test route for auction components
Route::get('/test-auction-components', function () {
    return view('test-auction-components');
})->name('test.auction.components');

// Simple test route for auction components
Route::get('/test-auction-simple', function () {
    return view('test-auction-simple');
})->name('test.auction.simple');

Route::get('/email/verify', function () {
    return view('auth.verify');
})->middleware('auth')->name('verification.notice');


Route::post('/email/verification-notification', function (Request $request) {
    $request->user()->sendEmailVerificationNotification();

    return back()->with('message', 'Verification link sent!');
})->middleware(['auth', 'throttle:6,1'])->name('verification.send');

// Route::get('email/verify', [VerificationController::class, 'show'])->name('verification.notice');
Route::get('/email/verify/{id}/{hash}', [VerificationController::class, 'verify'])->name('verification.verify');
// Route::get('email/verify/{id}', [VerificationController::class, 'verify'])->name('verification.verify');
Route::get('email/resend', [VerificationController::class, 'resend'])->name('verification.resend');
Route::post('email/resend', [VerificationController::class, 'resend'])->name('verification.resend');

Route::get('/', [HomeController::class, 'welcome']);

Route::get('/zoom', [ZoomController::class, 'zoom']);
Route::get('/zoom-auth', [ZoomController::class, 'zoomAuth']);

Route::get('/trust-policy', function(){
    return view("zoom-policy");
});

Route::get('/login', function () {
    return redirect('/');
})->name("login");

// Custom login POST route using the custom LoginController
Route::post('/login', [App\Http\Controllers\Auth\LoginController::class, 'login']);

Route::controller(PaymentController::class)
->prefix('paypal')
->group(function () {
    Route::view('payment', 'paypal.index')->name('create.payment');
    Route::get('handle-payment', 'handlePayment')->name('make.payment');
    Route::get('cancel-payment', 'paymentCancel')->name('cancel.payment');
    Route::get('payment-success', 'paymentSuccess')->name('success.payment');
    Route::get('payment-notify', 'paymentNotify')->name('notify.payment');
});

// DPO Pay Routes with rate limiting and security
Route::controller(PaymentController::class)
->prefix('dpopay')
->middleware(['web', \App\Http\Middleware\DPOPayRateLimit::class])
->group(function () {
    Route::view('payment', 'dpopay.index')->name('create.dpopay.payment');
    Route::view('test', 'dpopay.test')->name('dpopay.test'); // Test page with production URLs

    // Test routes for success/failure pages (remove in production)
    Route::get('test-success', function() {
        $payment = new \App\Models\DumpPayment([
            'amount' => 450.00,
            'currency' => 'USD',
            'company_ref' => 'VERTIGO-********-E1500A9C',
            'customer_email' => '<EMAIL>',
            'payment_date' => now(),
            'status' => 'completed'
        ]);

        return view('dpopay.success', [
            'payment' => $payment,
            'transaction_id' => 'F2B56B76-5AB9-428D-8E50-FD1E8A8D58D2',
            'approval_code' => '**********',
            'reference' => 'VERTIGO-********-E1500A9C',
            'amount' => 450.00,
            'currency' => 'USD'
        ]);
    })->name('dpopay.test.success');

    Route::get('test-failure', function() {
        return view('dpopay.failure', [
            'error' => 'Payment was declined by your bank.',
            'details' => 'Your card was declined. Please try a different payment method.',
            'can_retry' => true
        ]);
    })->name('dpopay.test.failure');
    Route::get('handle-payment', 'handleDPOPayment')->name('make.dpopay.payment');
    Route::post('checkout-payment', 'handleCheckoutDPOPayment')->name('checkout.dpopay.payment');
    Route::get('cancel-payment', 'dpoPaymentCancel')->name('dpopay.cancel');
    Route::get('payment-success', 'dpoPaymentSuccess')->name('dpopay.success');
    Route::get('payment-failure', 'dpoPaymentCancel')->name('dpopay.failure'); // Alias for failure

    // Webhook endpoint with CSRF exemption (handled separately)
    Route::post('payment-notify', 'dpoPaymentNotify')
        ->name('dpopay.notify')
        ->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class]);
});



// Route::get('/email/verify', function () {
//     return view('auth.verify');
// })->middleware('auth')->name('verification.notice');

// Route::get('/email/verify/{id}/{hash}', function (EmailVerificationRequest $request) {
//     $request->fulfill();

//     return redirect('/home');
// })->middleware(['auth', 'signed'])->name('verification.verify');

// Route::post('/email/verification-notification', function (Request $request) {
//     $request->user()->sendEmailVerificationNotification();

//     return back()->with('message', 'Verification link sent!');
// })->middleware(['auth', 'throttle:6,1'])->name('verification.send');

Route::get('/fix', function() {


    \App\Models\Status::create(['id' => 20, 'name' => 'Client owned']);
    \App\Models\Status::create(['id' => 21, 'name' => 'Trust owned']);
    \App\Models\Status::create(['id' => 22, 'name' => 'Suspense owned']);

    // Permission::create(['name' => 'sales report']);
    // Permission::create(['name' => 'inventory report']);
    // Permission::create(['name' => 'refund list report']);
    // Permission::create(['name' => 'deposits report']);
    // Permission::create(['name' => 'winners report']);

    // Permission::create(['name' => 'list orders']);
    // Permission::create(['name' => 'view orders']);
    // Permission::create(['name' => 'create orders']);
    // Permission::create(['name' => 'update orders']);
    // Permission::create(['name' => 'delete orders']);

    // Permission::create(['name' => 'list sales']);
    // Permission::create(['name' => 'view sales']);
    // Permission::create(['name' => 'create sales']);
    // Permission::create(['name' => 'update sales']);
    // Permission::create(['name' => 'delete sales']);

    echo "Done";
});

Route::resource('dataverses', DataVerseController::class);
Route::get('/dataverses.auth',[ DataVerseController::class, 'dataversesAuth'])->name("dataverses.auth");

Route::get('/shop', [HomeController::class, 'shop'])->name('shop');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/admin-login', function(){
    return view('auth.admin-login');
})->name('admin-login');

// Public cart routes (no authentication required)
Route::get('/view-item/{item}', [ItemController::class, 'viewItem']);
Route::get('/view-cash-item/{item}', [ItemController::class, 'viewCashItem']);
Route::get('/add-to-cart/{item}', [ItemController::class, 'addToCart']);
Route::get('/remove-from-cart/{item}', [ItemController::class, 'removeFromCart']);

// Cart API routes (using web middleware for session support)
Route::prefix('api')->group(function () {
    // Cart routes (public for now, can be moved to auth middleware later)
    Route::get('/cart', function() {
        $items = \Facades\App\Cache\Repo::cart();
        return response()->json($items);
    });

    // Add item to cart API
    Route::post('/cart/add/{item}', function(\App\Models\Item $item) {
        try {
            $type = $item->auctionType->type ?? '';
            if (!$type) {
                return response()->json(['success' => false, 'message' => 'Invalid item type'], 400);
            }

            // Get current cart items and quantities
            $items = session($type) ?? [];
            $quantities = session($type . '_quantities') ?? [];
            $quantity = request()->input('quantity', 1);

            // Validate using CartValidator
            $validation = \App\Validators\CartValidator::validateAddToCart($item, $quantity, $items);
            if (!$validation['valid']) {
                return response()->json(['success' => false, 'message' => $validation['message']], 400);
            }

            // Add item to cart if not already present
            if (!in_array($item->id, $items)) {
                $items[] = $item->id;
                $quantities[$item->id] = $quantity;
            } else {
                // Update quantity if item already in cart
                $quantities[$item->id] = ($quantities[$item->id] ?? 1) + $quantity;
            }

            // Save to session
            session([$type => $items]);
            session([$type . '_quantities' => $quantities]);

            return response()->json([
                'success' => true,
                'message' => $item->name . ' added to cart',
                'quantity' => $quantities[$item->id],
                'cart_count' => count(session('cash', [])) + count(session('online', [])) + count(session('live', []))
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to add item to cart'], 500);
        }
    });

    // Remove item from cart API
    Route::delete('/cart/remove/{item}', function(\App\Models\Item $item) {
        try {
            $type = $item->auctionType->type ?? '';
            if (!$type) {
                return response()->json(['success' => false, 'message' => 'Invalid item type'], 400);
            }

            // Get current cart items and quantities
            $items = session($type) ?? [];
            $quantities = session($type . '_quantities') ?? [];
            $quantityToRemove = request()->input('quantity', null);

            // If no specific quantity provided, remove entire item
            if ($quantityToRemove === null) {
                $items = array_diff($items, [$item->id]);
                unset($quantities[$item->id]);
                $message = $item->name . ' removed from cart';
            } else {
                // Reduce quantity
                $currentQuantity = $quantities[$item->id] ?? 1;
                $newQuantity = $currentQuantity - $quantityToRemove;

                if ($newQuantity <= 0) {
                    // Remove item completely if quantity becomes 0 or negative
                    $items = array_diff($items, [$item->id]);
                    unset($quantities[$item->id]);
                    $message = $item->name . ' removed from cart';
                } else {
                    $quantities[$item->id] = $newQuantity;
                    $message = $item->name . ' quantity reduced to ' . $newQuantity;
                }
            }

            // Save to session
            session([$type => $items]);
            session([$type . '_quantities' => $quantities]);

            return response()->json([
                'success' => true,
                'message' => $message,
                'cart_count' => count(session('cash', [])) + count(session('online', [])) + count(session('live', []))
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to remove item from cart'], 500);
        }
    });

    // Update cart item quantity API
    Route::patch('/cart/update/{item}', function(\App\Models\Item $item) {
        try {
            $type = $item->auctionType->type ?? '';
            if (!$type) {
                return response()->json(['success' => false, 'message' => 'Invalid item type'], 400);
            }

            $quantity = request()->input('quantity');
            if ($quantity === null) {
                return response()->json(['success' => false, 'message' => 'Quantity parameter required'], 400);
            }

            // Validate quantity (allow 0 for removal)
            if (!is_numeric($quantity) || $quantity < 0) {
                return response()->json(['success' => false, 'message' => 'Invalid quantity'], 400);
            }

            // Get current cart items and quantities
            $items = session($type) ?? [];
            $quantities = session($type . '_quantities') ?? [];

            // Check if item is in cart
            if (!in_array($item->id, $items)) {
                return response()->json(['success' => false, 'message' => 'Item not in cart'], 404);
            }

            // Validate item availability (except when removing)
            if ($quantity > 0) {
                $itemValidation = \App\Validators\CartValidator::validateItemForCart($item);
                if (!$itemValidation['valid']) {
                    return response()->json(['success' => false, 'message' => $itemValidation['message']], 400);
                }

                $quantityValidation = \App\Validators\CartValidator::validateQuantity($quantity);
                if (!$quantityValidation['valid']) {
                    return response()->json(['success' => false, 'message' => $quantityValidation['message']], 400);
                }
            }

            if ($quantity == 0) {
                // Remove item completely
                $items = array_diff($items, [$item->id]);
                unset($quantities[$item->id]);
                $message = $item->name . ' removed from cart';
            } else {
                // Update quantity
                $quantities[$item->id] = (int)$quantity;
                $message = $item->name . ' quantity updated to ' . $quantity;
            }

            // Save to session
            session([$type => $items]);
            session([$type . '_quantities' => $quantities]);

            return response()->json([
                'success' => true,
                'message' => $message,
                'quantity' => $quantity == 0 ? 0 : $quantities[$item->id] ?? 0,
                'cart_count' => count(session('cash', [])) + count(session('online', [])) + count(session('live', []))
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to update cart item'], 500);
        }
    });

    // Batch add multiple items to cart API
    Route::post('/cart/add-multiple', function() {
        try {
            $itemIds = request()->input('items', []);
            $defaultQuantity = request()->input('default_quantity', 1);

            if (empty($itemIds) || !is_array($itemIds)) {
                return response()->json(['success' => false, 'message' => 'Items array required'], 400);
            }

            $results = [];
            $successCount = 0;
            $failureCount = 0;

            foreach ($itemIds as $itemData) {
                try {
                    // Handle both simple ID arrays and objects with quantity
                    if (is_array($itemData)) {
                        $itemId = $itemData['id'] ?? null;
                        $quantity = $itemData['quantity'] ?? $defaultQuantity;
                    } else {
                        $itemId = $itemData;
                        $quantity = $defaultQuantity;
                    }

                    if (!$itemId) {
                        $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Invalid item ID'];
                        $failureCount++;
                        continue;
                    }

                    $item = \App\Models\Item::find($itemId);
                    if (!$item) {
                        $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Item not found'];
                        $failureCount++;
                        continue;
                    }

                    $type = $item->auctionType->type ?? '';
                    if (!$type) {
                        $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Invalid item type'];
                        $failureCount++;
                        continue;
                    }

                    // Add item to cart
                    $items = session($type) ?? [];
                    $quantities = session($type . '_quantities') ?? [];

                    if (!in_array($item->id, $items)) {
                        $items[] = $item->id;
                        $quantities[$item->id] = $quantity;
                    } else {
                        $quantities[$item->id] = ($quantities[$item->id] ?? 1) + $quantity;
                    }

                    session([$type => $items]);
                    session([$type . '_quantities' => $quantities]);

                    $results[] = [
                        'id' => $itemId,
                        'success' => true,
                        'message' => $item->name . ' added',
                        'quantity' => $quantities[$item->id]
                    ];
                    $successCount++;

                } catch (\Exception $e) {
                    $results[] = ['id' => $itemId ?? 'unknown', 'success' => false, 'message' => 'Error adding item'];
                    $failureCount++;
                }
            }

            return response()->json([
                'success' => $failureCount === 0,
                'message' => "Added {$successCount} items, {$failureCount} failed",
                'results' => $results,
                'summary' => [
                    'total' => count($itemIds),
                    'success' => $successCount,
                    'failed' => $failureCount
                ],
                'cart_count' => count(session('cash', [])) + count(session('online', [])) + count(session('live', []))
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to add items to cart'], 500);
        }
    });

    // Batch remove multiple items from cart API
    Route::delete('/cart/remove-multiple', function() {
        try {
            $itemIds = request()->input('items', []);

            if (empty($itemIds) || !is_array($itemIds)) {
                return response()->json(['success' => false, 'message' => 'Items array required'], 400);
            }

            $results = [];
            $successCount = 0;
            $failureCount = 0;

            foreach ($itemIds as $itemId) {
                try {
                    $item = \App\Models\Item::find($itemId);
                    if (!$item) {
                        $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Item not found'];
                        $failureCount++;
                        continue;
                    }

                    $type = $item->auctionType->type ?? '';
                    if (!$type) {
                        $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Invalid item type'];
                        $failureCount++;
                        continue;
                    }

                    // Remove item from cart
                    $items = session($type) ?? [];
                    $quantities = session($type . '_quantities') ?? [];

                    if (in_array($item->id, $items)) {
                        $items = array_diff($items, [$item->id]);
                        unset($quantities[$item->id]);

                        session([$type => $items]);
                        session([$type . '_quantities' => $quantities]);

                        $results[] = ['id' => $itemId, 'success' => true, 'message' => $item->name . ' removed'];
                        $successCount++;
                    } else {
                        $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Item not in cart'];
                        $failureCount++;
                    }

                } catch (\Exception $e) {
                    $results[] = ['id' => $itemId, 'success' => false, 'message' => 'Error removing item'];
                    $failureCount++;
                }
            }

            return response()->json([
                'success' => $failureCount === 0,
                'message' => "Removed {$successCount} items, {$failureCount} failed",
                'results' => $results,
                'summary' => [
                    'total' => count($itemIds),
                    'success' => $successCount,
                    'failed' => $failureCount
                ],
                'cart_count' => count(session('cash', [])) + count(session('online', [])) + count(session('live', []))
            ]);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to remove items from cart'], 500);
        }
    });
});
Route::get('/cart', function () {
    return view('spa');
})->name('cart');
Route::get('/checkout', function () {
    return view('spa');
})->name('checkout');
Route::post('/checkout', [ItemController::class, 'checkoutSave']);

// Payment success page (Vue component)
Route::get('/payment-success', function () {
    return view('spa');
})->name('payment.success');

// Test cart clearing (for debugging)
Route::get('/test-clear-cart', function () {
    try {
        // Get current cart before clearing
        $cartBefore = \Facades\App\Cache\Repo::cart();

        // Clear cart sessions
        session()->forget(['cash', 'online', 'live']);

        // Get cart after clearing
        $cartAfter = \Facades\App\Cache\Repo::cart();

        return response()->json([
            'success' => true,
            'message' => 'Cart cleared successfully',
            'cart_before' => [
                'count' => $cartBefore->count(),
                'items' => $cartBefore->pluck('id', 'name')->toArray()
            ],
            'cart_after' => [
                'count' => $cartAfter->count(),
                'items' => $cartAfter->pluck('id', 'name')->toArray()
            ],
            'sessions_before' => [
                'cash' => session('cash') ?? [],
                'online' => session('online') ?? [],
                'live' => session('live') ?? []
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
});

// Test order completion and cart clearing
Route::get('/test-order-completion', function () {
    try {
        // Create a dummy payment record for testing
        $payment = new \App\Models\DumpPayment();
        $payment->id = 999; // Dummy ID
        $payment->company_ref = 'TEST-' . time();
        $payment->amount = 25000;
        $payment->currency = 'MWK';
        $payment->status = 'pending';
        $payment->data = json_encode(['test' => 'data']);

        // Get cart before clearing
        $cartBefore = \Facades\App\Cache\Repo::cart();

        // Call the order completion service
        $result = \App\Services\OrderCompletionService::completeOrder($payment, [
            'test_mode' => true,
            'trans_id' => 'TEST-123',
            'approval_code' => 'APP-456'
        ]);

        // Get cart after clearing
        $cartAfter = \Facades\App\Cache\Repo::cart();

        return response()->json([
            'success' => true,
            'message' => 'Order completion test completed',
            'completion_result' => $result,
            'cart_before' => [
                'count' => $cartBefore->count(),
                'items' => $cartBefore->pluck('id', 'name')->toArray()
            ],
            'cart_after' => [
                'count' => $cartAfter->count(),
                'items' => $cartAfter->pluck('id', 'name')->toArray()
            ]
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
});

Route::prefix('/')->middleware(['auth', 'verified'])->group(function () {

    Route::post('/cancel-bid/{auction}', [AuctionController::class, 'cancelBid']);
    Route::get('/cancel-bid/{auction}', [AuctionController::class, 'cancelBid']);
    Route::get('/profile', [UserController::class, 'profile'])->name('profile');
    Route::post('/profile', [UserController::class, 'saveProfile'])->name('profile');
    Route::post('/place-a-bid/{item}', [ AuctionController::class, 'placeABid' ]);
    Route::post('/ajax-bid', [ AuctionController::class, 'ajaxBid' ]);
    Route::get('/register-bid', [ AuctionController::class, 'registerBid' ]);
    Route::post('/register-bid', [ AuctionController::class, 'registerBidSave' ]);
    Route::get('/ajax-auction/{auction}', [ AuctionController::class, 'ajaxAuction' ]);

    Route::middleware('staffMiddleware')->group(function () {

        Route::get('/home', [HomeController::class, 'index'])->name('home');
        Route::get('/admin-modernized', [HomeController::class, 'modernizedAdmin'])->name('admin.modernized');


        Route::resource('orders', OrderController::class);
        Route::get('/ajax-order/{order}', [OrderController::class, 'ajaxOrder']);
        Route::post('/sale-bid', [ OrderController::class, 'saleBid' ]);

        // Modernized sales/orders routes
        Route::get('/orders-modernized', [OrderController::class, 'modernizedIndex'])->name('orders.modernized.index');
        Route::get('/orders-modernized/create', [OrderController::class, 'modernizedCreate'])->name('orders.modernized.create');
        Route::get('/orders-modernized/{order}', [OrderController::class, 'modernizedShow'])->name('orders.modernized.show');
        Route::get('/orders-modernized/{order}/edit', [OrderController::class, 'modernizedEdit'])->name('orders.modernized.edit');
        Route::delete('/orders-bulk-delete', [OrderController::class, 'bulkDelete'])->name('orders.bulk-delete');
        Route::post('/orders-bulk-export', [OrderController::class, 'bulkExport'])->name('orders.bulk-export');

        // Modernized auctions routes
        Route::get('/auctions-modernized', [AuctionController::class, 'modernizedIndex'])->name('auctions.modernized.index');
        Route::get('/auctions-modernized/{auction}', [AuctionController::class, 'modernizedShow'])->name('auctions.modernized.show');

        // Modernized auction types routes
        Route::get('/auction-types-modernized', [AuctionTypeController::class, 'modernizedIndex'])->name('auction-types.modernized.index');
        Route::get('/auction-types-modernized/create', [AuctionTypeController::class, 'modernizedCreate'])->name('auction-types.modernized.create');
        Route::get('/auction-types-modernized/{auctionType}', [AuctionTypeController::class, 'modernizedShow'])->name('auction-types.modernized.show');
        Route::get('/auction-types-modernized/{auctionType}/edit', [AuctionTypeController::class, 'modernizedEdit'])->name('auction-types.modernized.edit');

        // Modernized auction listing routes
        Route::get('/auction-listing-modernized/create', [AuctionListingController::class, 'modernizedCreate'])->name('auction-listing.modernized.create');

        // Modernized items routes
        Route::get('/items-modernized', [ItemController::class, 'modernizedIndex'])->name('items.modernized.index');
        Route::get('/items-modernized/create', [ItemController::class, 'modernizedCreate'])->name('items.modernized.create');
        Route::get('/items-modernized/{item}', [ItemController::class, 'modernizedShow'])->name('items.modernized.show');
        Route::get('/items-modernized/{item}/edit', [ItemController::class, 'modernizedEdit'])->name('items.modernized.edit');

        Route::resource('branches', BranchController::class);

        Route::resource('roles', RoleController::class);

        Route::resource('permissions', PermissionController::class);

        Route::resource('accounts', AccountController::class);
        Route::resource('adverts', AdvertController::class);
        Route::resource('auction-types', AuctionTypeController::class);
        Route::resource('auction-listing', AuctionListingController::class);

        Route::get('/accept-bid/{auction}', [ AuctionController::class, 'acceptBid']);
        Route::post('/accept-bid/{auction}', [ AuctionController::class, 'acceptBid']);

        Route::resource('users', UserController::class);
        Route::resource('/suppliers', SupplierController::class);
        Route::get('/set-branch/{branch}', [UserController::class, 'setBranch']);
        Route::post('/delete-media/{media}', [UserController::class, 'deleteMedia']);
        Route::get('/delete-media-get/{media}', [UserController::class, 'deleteMediaGet']);

        Route::resource('transactions', TransactionController::class);
        Route::post('/import-bank-statement', [TransactionController::class, 'importStatement'])->name('transactions.importStatement');


        Route::resource('statuses', StatusController::class);

        Route::resource('items', ItemController::class);
        Route::get('/notifications', [HomeController::class, "notifications"])->name("notifications");

        Route::resource('auctions', AuctionController::class);

        Route::get('refund-list-report', [HomeController::class, 'refundListReport']);
        Route::get('winners-report', [HomeController::class, 'winnersReport']);
        Route::get('sales-report', [HomeController::class, 'salesReport']);
        Route::get('inventory-report', [HomeController::class, 'inventoryReport']);
        Route::get('deposits-report', [HomeController::class, 'depositsReport']);

        Route::resource('settings', SettingController::class);
    });

});

Route::get('/ajax-items', [ItemController::class, "ajaxItems"]);
Route::get('/ajax-auction-types', [AuctionTypeController::class, "ajaxAuctionTypes"]);
Route::get('/ajax-branches', [BranchController::class, "ajaxBranches"]);
Route::get('/ajax-item/{item}', [ItemController::class, "ajaxItem"]);

// Test route for Vue components
Route::get('/test-vue', function () {
    return view('test-vue');
});

// Test route for SPA homepage
Route::get('/spa', function () {
    return view('spa');
})->name('spa.homepage');

// New Vue 3 homepage (alternative to current homepage)
Route::get('/home-vue', function () {
    return view('spa');
})->name('homepage.vue');

// Vue 3 item detail page
Route::get('/item/{item}', function () {
    return view('spa');
})->name('item.detail.vue');

// SPA register-bid page
Route::get('/spa/register-bid', function () {
    return view('spa');
})->name('spa.register-bid')->middleware(['auth', 'verified']);

// SPA bid-dashboard page
Route::get('/spa/bid-dashboard', function () {
    return view('spa');
})->name('spa.bid-dashboard')->middleware(['auth', 'verified']);

// Bid dashboard page (both SPA and regular)
Route::get('/bid-dashboard', function () {
    return view('spa');
})->name('bid-dashboard')->middleware(['auth', 'verified']);

// Session-based user route for SPA authentication check
Route::get('/api/user-session', function () {
    if (auth()->check()) {
        return response()->json(auth()->user());
    }
    return response()->json(['authenticated' => false], 401);
})->name('api.user.session');

// Session-based live auctions endpoint for SPA
Route::middleware('auth')->get('/api/live-auctions-session', [App\Http\Controllers\Api\AuctionController::class, 'liveAuctions'])->name('api.live.auctions.session');

// Additional SPA routes for better navigation
Route::get('/spa/cart', function () {
    return view('spa');
})->name('spa.cart');

Route::get('/spa/checkout', function () {
    return view('spa');
})->name('spa.checkout');

Route::get('/spa/payment-success', function () {
    return view('spa');
})->name('spa.payment-success');

Route::get('/spa/item/{item}', function () {
    return view('spa');
})->name('spa.item.detail');

// Test route for SPA debugging
Route::get('/spa-test', function () {
    return view('spa-test');
})->name('spa.test');

// Test API endpoints
Route::get('/test-api', function () {
    return response()->json([
        'message' => 'API is working',
        'timestamp' => now(),
        'branches' => \App\Models\Branch::count(),
        'items_endpoint' => url('/ajax-items'),
        'auction_types_endpoint' => url('/ajax-auction-types')
    ]);
});

// API route for adverts
Route::get('/api/adverts', function () {
    return response()->json(\App\Models\Advert::all());
});

// Session-based user endpoint for Vue.js authentication
Route::get('/api/user', function () {
    if (auth()->check()) {
        $user = auth()->user();
        $user->load('roles'); // Load roles if needed
        return response()->json($user);
    }
    return response()->json(null, 401);
})->name('api.session.user');

// Session-based API endpoints for Vue.js
Route::middleware('auth')->group(function () {
    // Watchlist endpoints
    Route::get('/api/watchlist', [App\Http\Controllers\Api\WatchlistController::class, 'index'])->name('api.session.watchlist.index');
    Route::post('/api/watchlist', [App\Http\Controllers\Api\WatchlistController::class, 'store'])->name('api.session.watchlist.store');
    Route::delete('/api/watchlist/{itemId}', [App\Http\Controllers\Api\WatchlistController::class, 'destroy'])->name('api.session.watchlist.destroy');
    Route::get('/api/watchlist/check/{itemId}', [App\Http\Controllers\Api\WatchlistController::class, 'check'])->name('api.session.watchlist.check');
    Route::get('/api/watchlist/count', [App\Http\Controllers\Api\WatchlistController::class, 'count'])->name('api.session.watchlist.count');
    Route::post('/api/watchlist/toggle', [App\Http\Controllers\Api\WatchlistController::class, 'toggle'])->name('api.session.watchlist.toggle');

    // Bid dashboard endpoint
    Route::get('/api/bid-dashboard', [App\Http\Controllers\Api\BidDashboardController::class, 'index'])->name('api.session.bid-dashboard');
});

// SPA Catch-all route - MUST BE LAST
// This handles all SPA routes that don't match other routes
Route::get('/spa/{any}', function () {
    return view('spa');
})->where('any', '.*')->name('spa.catchall');
