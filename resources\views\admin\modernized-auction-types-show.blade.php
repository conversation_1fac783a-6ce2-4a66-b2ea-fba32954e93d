@extends('layouts.modernized-admin')

@section('title', $auctionType->name . ' - Auction Category - Vertigo AMS')

@section('page-title', $auctionType->name)
@section('page-subtitle', 'View and manage this auction category and its products.')

@section('quick-actions')
<div class="flex space-x-2">
    @can('update', $auctionType)
    <a href="{{ route('auction-types.modernized.edit', $auctionType) }}" class="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
        <span class="hidden lg:inline">Edit Category</span>
        <span class="lg:hidden">Edit</span>
    </a>
    @endcan
    
    @can('create', App\Models\Item::class)
    <a href="{{ route('items.create', ['auction_type_id' => $auctionType->id]) }}" class="flex items-center bg-white text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 shadow border border-gray-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <span class="hidden lg:inline">Add Product</span>
        <span class="lg:hidden">Add</span>
    </a>
    @endcan
    
    <a href="{{ route('auction-types.modernized.index') }}" class="flex items-center bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-all duration-200">
        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        <span class="hidden lg:inline">Back to Categories</span>
        <span class="lg:hidden">Back</span>
    </a>
</div>
@endsection

@section('content')
<!-- Category Overview -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Category Details Card -->
    <div class="lg:col-span-2">
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Category Details</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Category Name</label>
                        <p class="text-lg font-semibold text-gray-900">{{ $auctionType->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Category Type</label>
                        @php
                            $typeColors = [
                                'online' => 'bg-green-100 text-green-800',
                                'live' => 'bg-red-100 text-red-800',
                                'cash' => 'bg-purple-100 text-purple-800'
                            ];
                            $typeColor = $typeColors[$auctionType->type] ?? 'bg-gray-100 text-gray-800';
                        @endphp
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $typeColor }}">
                            {{ ucfirst($auctionType->type ?? '-') }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Created By</label>
                        <p class="text-gray-900">{{ optional($auctionType->createdBy)->name ?? 'Unknown' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Created Date</label>
                        <p class="text-gray-900">{{ $auctionType->created_at->format('M j, Y \a\t g:i A') }}</p>
                    </div>
                </div>
                
                @if($auctionType->description)
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-500 mb-1">Description</label>
                    <p class="text-gray-900">{{ $auctionType->description }}</p>
                </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Stats Card -->
    <div class="space-y-6">
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">Active Items</span>
                    <span class="text-lg font-semibold text-gray-900">{{ $auctionType->items()->whereNull('closed_by')->count() }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">Total Items</span>
                    <span class="text-lg font-semibold text-gray-900">{{ $auctionType->items()->count() }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500">Last Updated</span>
                    <span class="text-sm text-gray-900">{{ $auctionType->updated_at->diffForHumans() }}</span>
                </div>
            </div>
        </div>
        
        <!-- Actions Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-3">
                @can('update', $auctionType)
                <a href="{{ route('auction-types.modernized.edit', $auctionType) }}" class="block w-full text-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 font-medium">
                    Edit Category
                </a>
                @endcan
                
                @can('create', App\Models\Item::class)
                <a href="{{ route('items.create', ['auction_type_id' => $auctionType->id]) }}" class="block w-full text-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium">
                    Add Product
                </a>
                @endcan
                
                <a href="{{ route('auction-types.show', $auctionType) }}" class="block w-full text-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200 font-medium">
                    Original View
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Category Images -->
@php $images = $auctionType->getMedia('media') @endphp
@if($images->count() > 0)
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Category Images</h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            @foreach($images as $image)
            <div class="relative group">
                <img src="{{ $image->getUrl() }}" alt="Category image" class="w-full h-24 object-cover rounded-lg border border-gray-200 group-hover:shadow-lg transition-shadow duration-200">
                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
                    <a href="{{ $image->getUrl() }}" target="_blank" class="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </a>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>
@endif

<!-- Products List -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <div>
            <h3 class="text-lg font-semibold text-gray-900">Products in this Category</h3>
            <p class="text-sm text-gray-600 mt-1">{{ $auctionType->items()->whereNull('closed_by')->count() }} active products</p>
        </div>
        @can('create', App\Models\Item::class)
        <a href="{{ route('items.create', ['auction_type_id' => $auctionType->id]) }}" class="flex items-center bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200 font-medium">
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Product
        </a>
        @endcan
    </div>
    
    @if($auctionType->items->count() > 0)
    <div class="p-6">
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
            @foreach($auctionType->items as $item)
            <div class="group bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200 border border-gray-200 hover:border-primary-300">
                <a href="{{ route('items.show', $item) }}" class="block">
                    <div class="aspect-w-1 aspect-h-1">
                        <img src="{{ $item->image ?? asset('assets/img/placeholder.jpg') }}" 
                             alt="{{ $item->name }}" 
                             class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-200">
                    </div>
                    <div class="p-4">
                        <h4 class="text-sm font-medium text-gray-900 truncate group-hover:text-primary-600 transition-colors duration-200">
                            {{ $item->name }}
                        </h4>
                        @if($item->starting_price)
                        <p class="text-sm text-gray-500 mt-1">
                            Starting: {{ _money($item->starting_price) }}
                        </p>
                        @endif
                        @if($item->closed_by)
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-2">
                            Closed
                        </span>
                        @else
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-2">
                            Active
                        </span>
                        @endif
                    </div>
                </a>
            </div>
            @endforeach
        </div>
    </div>
    @else
    <div class="p-12 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No products in this category</h3>
        <p class="text-gray-500 mb-4">Get started by adding your first product to this category.</p>
        @can('create', App\Models\Item::class)
        <a href="{{ route('items.create', ['auction_type_id' => $auctionType->id]) }}" class="inline-flex items-center bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200 font-medium">
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add First Product
        </a>
        @endcan
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Modernized Auction Category Show page loaded');
});
</script>
@endpush
